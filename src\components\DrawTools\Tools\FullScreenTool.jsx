import { useEffect } from "react";
import <PERSON>n<PERSON> from "konva";
import { FaMinusCircle } from "react-icons/fa";
import { useTool } from "../../../contexts/ToolContext";
import { useFullscreen } from "../../../contexts/FullscreenContext";

function FullScreenTool({ stageRef, shapes, setShapes }) {
  const { activeTool, setActiveTool } = useTool();
  const { isFullscreenActive, setIsFullscreenActive } = useFullscreen();
  
  useEffect(() => {
    if (!stageRef || !stageRef.current) return;
    
    const stage = stageRef.current;
    const layer = stage.findOne("Layer");
    if (!layer) return;
    
    // If fullscreen tool is activated
    if (activeTool === "fullscreen") {
      // Toggle frame visibility
      if (isFullscreenActive) {
        // If frame exists, remove it and remove icons
        layer.find(".fullscreen-frame").forEach(frame => frame.destroy());
        layer.find(".remove-icon").forEach(icon => icon.destroy());
        layer.draw();
        setIsFullscreenActive(false);
      } else {
        // If no frame exists, create one
        createFullscreenFrame(layer);
        setIsFullscreenActive(true);
      }
      
      // Always deactivate the tool after toggling
      setActiveTool(null);
    }
    
    return () => {
      // Cleanup function - no need to remove frame here
    };
  }, [activeTool, stageRef, setActiveTool, isFullscreenActive, setIsFullscreenActive]);
  
  // Function to create the fullscreen frame and remove icons
  const createFullscreenFrame = (layer) => {
    // Remove any existing frames and remove icons first
    layer.find(".fullscreen-frame").forEach(frame => frame.destroy());
    layer.find(".remove-icon").forEach(icon => icon.destroy());

    // Calculate bounding box for all objects
    const stageShapes = layer.children.filter(child =>
      !child.hasName("fullscreen-frame") &&
      !child.hasName("remove-icon") &&
      child.className !== "Layer"
    );

    if (stageShapes.length === 0) {
      // No shapes to frame
      return;
    }

    // Get bounding box for all shapes
    const box = {
      x: Infinity,
      y: Infinity,
      width: 0,
      height: 0,
      right: 0,
      bottom: 0
    };

    stageShapes.forEach(shape => {
      const rect = shape.getClientRect();
      box.x = Math.min(box.x, rect.x);
      box.y = Math.min(box.y, rect.y);
      box.right = Math.max(box.right, rect.x + rect.width);
      box.bottom = Math.max(box.bottom, rect.y + rect.height);
    });

    box.width = box.right - box.x;
    box.height = box.bottom - box.y;

    // Add padding
    const padding = 20;
    box.x -= padding;
    box.y -= padding;
    box.width += padding * 2;
    box.height += padding * 2;

    // Create frame
    const frame = new Konva.Rect({
      x: box.x,
      y: box.y,
      width: box.width,
      height: box.height,
      stroke: "#07a4fe",
      strokeWidth: 2,
      dash: [10, 5],
      name: "fullscreen-frame",
      listening: false
    });

    layer.add(frame);

    // Add remove icons to circles
    createRemoveIcons(layer);

    layer.draw();
  };

  // Function to create remove icons for circles
  const createRemoveIcons = (layer) => {
    if (!shapes || !setShapes) return;

    // Find all circle shapes in the shapes state
    const circleShapes = shapes.filter(shape => shape.type === "circle");

    circleShapes.forEach(circleShape => {
      // Create a group for the remove icon
      const iconGroup = new Konva.Group({
        x: circleShape.x,
        y: circleShape.y,
        name: "remove-icon",
        id: `remove-icon-${circleShape.id}`
      });

      // Create background circle for the icon
      const iconBg = new Konva.Circle({
        x: 0,
        y: 0,
        radius: 12,
        fill: "white",
        stroke: "#dc2626",
        strokeWidth: 2,
        shadowColor: "black",
        shadowBlur: 4,
        shadowOffset: { x: 2, y: 2 },
        shadowOpacity: 0.3
      });

      // Create the minus circle icon using Konva shapes
      const minusIcon = new Konva.Circle({
        x: 0,
        y: 0,
        radius: 8,
        fill: "#dc2626"
      });

      const minusLine = new Konva.Line({
        points: [-4, 0, 4, 0],
        stroke: "white",
        strokeWidth: 2,
        lineCap: "round"
      });

      // Add click handler
      iconGroup.on("click tap", () => handleRemoveClick(circleShape.id));
      iconGroup.on("mouseenter", () => {
        document.body.style.cursor = "pointer";
        iconBg.fill("#fee2e2");
        layer.draw();
      });
      iconGroup.on("mouseleave", () => {
        document.body.style.cursor = "default";
        iconBg.fill("white");
        layer.draw();
      });

      iconGroup.add(iconBg);
      iconGroup.add(minusIcon);
      iconGroup.add(minusLine);
      layer.add(iconGroup);
    });
  };

  // Function to handle remove icon clicks
  const handleRemoveClick = (circleId) => {
    if (!shapes || !setShapes) return;

    // Remove all lineWithLabel elements associated with this circle
    const updatedShapes = shapes.filter(shape => {
      // Keep all shapes except lineWithLabel ones
      if (shape.type !== "lineWithLabel") {
        return true;
      }
      // For lineWithLabel, we need to determine if it's associated with this circle
      // This might need adjustment based on how your lineWithLabel elements are linked to circles
      return false; // For now, remove all lineWithLabel elements when any circle's remove icon is clicked
    });

    setShapes(updatedShapes);

    // Also remove the remove icons from the stage
    const stage = stageRef.current;
    if (stage) {
      const layer = stage.findOne("Layer");
      if (layer) {
        layer.find(".remove-icon").forEach(icon => icon.destroy());
        layer.draw();
      }
    }
  };

  return null;
}

export default FullScreenTool;


